package com.xiang.chat.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * JWT Token提供者
 * 用于从当前安全上下文中获取JWT token
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenProvider {

    // 使用ThreadLocal存储当前请求的token，支持跨线程传递
    private static final ThreadLocal<String> TOKEN_HOLDER = new InheritableThreadLocal<>();

    /**
     * 获取当前用户的JWT token
     * @return JWT token字符串
     */
    public String getCurrentToken() {
        // 首先尝试从ThreadLocal获取（支持跨线程传递）
        String threadLocalToken = TOKEN_HOLDER.get();
        if (threadLocalToken != null) {
            log.debug("Retrieved token from ThreadLocal, length: {}", threadLocalToken.length());
            return threadLocalToken;
        }

        // 然后尝试从SecurityContext获取
        String userToken = getUserToken();
        if (userToken != null) {
            // 将token存储到ThreadLocal中，以便子线程使用
            TOKEN_HOLDER.set(userToken);
            return userToken;
        }

        // 如果SecurityContext中没有，尝试从RequestContext获取
        String requestToken = getTokenFromRequest();
        if (requestToken != null) {
            // 将token存储到ThreadLocal中，以便子线程使用
            TOKEN_HOLDER.set(requestToken);
            return requestToken;
        }

        log.warn("No token available for auth-service call");
        return null;
    }
    
    /**
     * 获取当前用户的JWT token
     * @return JWT token字符串，如果没有则返回null
     */
    private String getUserToken() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            log.debug("=== JWT Token Provider Debug ===");
            log.debug("Authentication present: {}", authentication != null);
            log.debug("Authentication type: {}", authentication != null ? authentication.getClass().getSimpleName() : "null");
            log.debug("Authentication name: {}", authentication != null ? authentication.getName() : "null");
            log.debug("Is authenticated: {}", authentication != null ? authentication.isAuthenticated() : false);
            
            // 首先尝试从JWT认证中获取token
            if (authentication instanceof JwtAuthenticationToken jwtAuth) {
                Jwt jwt = jwtAuth.getToken();
                String tokenValue = jwt.getTokenValue();
                log.debug("Found JwtAuthenticationToken, token length: {}", tokenValue != null ? tokenValue.length() : 0);
                log.debug("Token preview: {}", tokenValue != null ? tokenValue.substring(0, Math.min(20, tokenValue.length())) + "..." : "null");
                return tokenValue;
            }
            
            // 然后尝试从OAuth2认证结果的details中获取token
            if (authentication != null && authentication.getDetails() != null) {
                Object details = authentication.getDetails();
                log.debug("Authentication details type: {}", details.getClass().getSimpleName());
                
                if (details instanceof com.xiang.chat.service.OAuth2TokenResponse tokenResponse) {
                    String accessToken = tokenResponse.getAccessToken();
                    log.debug("Retrieved access token from OAuth2TokenResponse: {}", 
                        accessToken != null ? "present (length: " + accessToken.length() + ")" : "null");
                    return accessToken;
                }
            }
            
            log.debug("No user JWT token found in current security context");
            log.debug("=== End JWT Token Provider Debug ===");
            return null;
            
        } catch (Exception e) {
            log.warn("Failed to get current user JWT token", e);
            return null;
        }
    }

    /**
     * 从HTTP请求中获取JWT token
     * @return JWT token字符串，如果没有则返回null
     */
    private String getTokenFromRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                String authHeader = attributes.getRequest().getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring(7);
                    log.debug("Retrieved JWT token from request header, length: {}", token.length());
                    return token;
                }
            }
            return null;
        } catch (Exception e) {
            log.debug("Failed to get token from request", e);
            return null;
        }
    }

    /**
     * 手动设置token到ThreadLocal（用于测试或特殊场景）
     * @param token JWT token
     */
    public void setCurrentToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            TOKEN_HOLDER.set(token);
            log.debug("Manually set token to ThreadLocal, length: {}", token.length());
        } else {
            TOKEN_HOLDER.remove();
            log.debug("Removed token from ThreadLocal");
        }
    }

    /**
     * 清除ThreadLocal中的token
     */
    public void clearCurrentToken() {
        TOKEN_HOLDER.remove();
        log.debug("Cleared token from ThreadLocal");
    }

    /**
     * 检查当前是否有有效的JWT token
     * @return true如果有有效token，否则false
     */
    public boolean hasValidToken() {
        String token = getCurrentToken();
        return token != null && !token.trim().isEmpty();
    }
}