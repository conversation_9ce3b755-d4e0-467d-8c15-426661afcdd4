server:
  port: 8083

spring:
  application:
    name: chat-service
  cloud:
    openfeign:
      circuitbreaker:
        enabled: true
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    druid:
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      # 监控统计
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
        allow: 127.0.0.1
      # 监控过滤器
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        wall:
          enabled: true
        slf4j:
          enabled: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false

  # Redis配置
  data:
    redis:
      host: *************
      port: 6379
      password:
      database: 2
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 3000ms

  # Thymeleaf配置
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML
    encoding: UTF-8
    cache: false  # 开发环境关闭缓存
    servlet:
      content-type: text/html
# Spring Security OAuth2 Resource Server配置
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: http://localhost:9000/oauth2/jwks

# Feign配置 - 确保使用同步调用避免ThreadLocal问题
feign:
  hystrix:
    enabled: false
  circuitbreaker:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 10000
# OAuth2配置 - 使用密码模式
oauth2:
  auth-server:
    token-uri: http://localhost:9000/oauth2/token
    validation-uri: http://localhost:9000/api/jwt/validate
    client-id: chat-client
    client-secret: chat-secret


# RocketMQ配置
rocketmq:
  name-server: *************:9876
  metrics:
    enabled: false
  producer:
    group: chat-producer-group
    send-message-timeout: 3000
    compress-message-body-threshold: 4096
    max-message-size: 4194304
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    retry-next-server: true
    access-key: ${ROCKETMQ_ACCESS_KEY:}
    secret-key: ${ROCKETMQ_SECRET_KEY:}
  consumer:
    access-key: ${ROCKETMQ_ACCESS_KEY:}
    secret-key: ${ROCKETMQ_SECRET_KEY:}

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Netty配置
netty:
  websocket:
    port: 9090
    path: /ws
    max-frame-size: 65536
    max-connections: 10000
  tcp:
    port: 9091
    max-connections: 5000

# 聊天服务配置
chat:
  # 消息配置
  message:
    max-length: 1000
    history-size: 100
    offline-expire: 7d
  # 房间配置
  room:
    max-users: 500
    max-rooms: 1000
    idle-timeout: 30m
  # 文件上传配置
  file:
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,txt,zip,rar
    upload-path: ${java.io.tmpdir}/chat/files
  # RocketMQ主题配置
  mq:
    topics:
      chat-message: CHAT_MESSAGE_TOPIC
      notification: NOTIFICATION_TOPIC
      user-status: USER_STATUS_TOPIC
    tags:
      message-send: MESSAGE_SEND
      message-forward: MESSAGE_FORWARD
      room-broadcast: ROOM_BROADCAST
      user-online: USER_ONLINE
      user-offline: USER_OFFLINE



# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    binders:
      processor:
        enabled: false  # 禁用处理器指标以避免Docker环境下的NullPointerException

# 日志配置
logging:
  level:
    com.xiang.chat: DEBUG
    io.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
