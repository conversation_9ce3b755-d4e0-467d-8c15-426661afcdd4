package com.xiang.chat.netty.handler;

import com.alibaba.fastjson2.JSON;
import com.xiang.chat.dto.WebSocketMessage;
import com.xiang.chat.model.dto.ChatMessage;
import com.xiang.chat.model.AuthResult;
import com.xiang.chat.service.ChatMessageService;
import com.xiang.chat.service.ChatRoomService;
import com.xiang.chat.service.ConnectionManager;
import com.xiang.chat.service.OfflineMessageService;
import com.xiang.chat.service.UserInfoService;
import com.xiang.chat.dto.UserInfo;
import org.springframework.data.redis.core.RedisTemplate;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;

/**
 * WebSocket消息处理器
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ChannelHandler.Sharable
public class WebSocketHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

    private final ConnectionManager connectionManager;
    private final ChatMessageService chatMessageService;
    private final ChatRoomService chatRoomService;
    private final OfflineMessageService offlineMessageService;
    private final UserInfoService userInfoService;
    private final RedisTemplate<String, Object> redisTemplate;

    // 用于异步广播的线程池
    private final ExecutorService broadcastExecutor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r, "broadcast-thread");
        t.setDaemon(true);
        return t;
    });

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        log.info("WebSocket连接建立: channelId={}, remoteAddress={}, localAddress={}",
            ctx.channel().id().asShortText(), ctx.channel().remoteAddress(), ctx.channel().localAddress());

        // 设置一个标记，表示连接刚建立，还未完成握手
        ctx.channel().attr(io.netty.util.AttributeKey.valueOf("handshake_completed")).set(false);

        // 检查管道中的处理器
        log.info("当前管道处理器: {}", ctx.pipeline().names());

        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        Long userId = connectionManager.getUserId(ctx.channel());
        log.info("WebSocket连接断开: channelId={}, userId={}, remoteAddress={}",
            ctx.channel().id().asShortText(), userId, ctx.channel().remoteAddress());

        // 从连接管理器中移除连接
        if (userId != null) {
            log.info("开始清理用户连接: userId={}, channelId={}", userId, ctx.channel().id().asShortText());
        }
        connectionManager.removeConnection(ctx.channel());
        if (userId != null) {
            log.info("用户连接清理完成: userId={}, channelId={}", userId, ctx.channel().id().asShortText());
            // 异步广播用户列表更新（用户下线）
            broadcastUserListUpdateAsync();
        }

        // 强制清理 Channel 属性
        try {
            ctx.channel().attr(io.netty.util.AttributeKey.valueOf("handshake_completed")).set(null);
        } catch (Exception e) {
            log.warn("清理 Channel 属性失败", e);
        }

        super.channelInactive(ctx);
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
        // 标记握手已完成
        ctx.channel().attr(io.netty.util.AttributeKey.valueOf("handshake_completed")).set(true);

        if (frame instanceof TextWebSocketFrame) {
            handleTextFrame(ctx, (TextWebSocketFrame) frame);
        } else {
            log.warn("不支持的WebSocket帧类型: {}", frame.getClass().getSimpleName());
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextFrame(ChannelHandlerContext ctx, TextWebSocketFrame frame) {
        try {
            String text = frame.text();
            log.debug("收到WebSocket消息: {}", text);

            // 消息长度验证
            if (text.length() > 10000) { // 10KB限制
                sendErrorMessage(ctx, "消息长度超过限制");
                return;
            }

            // 解析消息
            WebSocketMessage wsMessage = parseWebSocketMessage(text);
            if (wsMessage == null) {
                sendErrorMessage(ctx, "无效的消息格式");
                return;
            }

            // 消息基本验证
            if (!validateMessage(wsMessage)) {
                sendErrorMessage(ctx, "消息验证失败");
                return;
            }

            // 根据消息类型处理
            switch (wsMessage.getType()) {
                case "auth":
                    handleAuthMessage(ctx, wsMessage);
                    break;
                case "chat":
                    handleChatMessage(ctx, wsMessage);
                    break;
                case "join_room":
                    handleJoinRoomMessage(ctx, wsMessage);
                    break;
                case "leave_room":
                    handleLeaveRoomMessage(ctx, wsMessage);
                    break;
                case "ping":
                    handlePingMessage(ctx, wsMessage);
                    break;
                case "heartbeat":
                    handleHeartbeatMessage(ctx, wsMessage);
                    break;
                case "get_online_users":
                    handleGetOnlineUsersMessage(ctx, wsMessage);
                    break;
                case "get_room_info":
                    handleGetRoomInfoMessage(ctx, wsMessage);
                    break;
                default:
                    log.warn("未知的消息类型: {}", wsMessage.getType());
                    sendErrorMessage(ctx, "未知的消息类型: " + wsMessage.getType());
            }

        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            sendErrorMessage(ctx, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理认证消息
     */
    private void handleAuthMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            // 检查是否已经认证

            if (connectionManager.getUserId(ctx.channel()) != null) {
                sendErrorMessage(ctx, "用户已认证，无需重复认证");
                return;
            }

            String token = (String) message.getData().get("token");
            if (token == null || token.trim().isEmpty()) {
                sendAuthFailureMessage(ctx, "缺少认证令牌", "MISSING_TOKEN");
                return;
            }

            // 检查认证频率限制
            if (!checkAuthRateLimit(ctx)) {
                sendAuthFailureMessage(ctx, "认证请求过于频繁，请稍后重试", "RATE_LIMIT");
                return;
            }

            // 验证token并获取用户信息
            log.info("开始认证用户: token={}, channelId={}", token, ctx.channel().id().asShortText());
            AuthResult authResult = connectionManager.authenticateUserWithDetails(token);
            if (!authResult.isSuccess()) {
                log.warn("用户认证失败: token={}, error={}, code={}", token, authResult.getErrorMessage(), authResult.getErrorCode());
                sendAuthFailureMessage(ctx, authResult.getErrorMessage(), authResult.getErrorCode());
                recordAuthFailure(ctx, authResult.getErrorCode());
                return;
            }

            Long userId = authResult.getUserId();
            log.info("用户认证成功: userId={}, token={}, channelId={}", userId, token, ctx.channel().id().asShortText());

            // 检查用户状态
            if (!isUserAllowedToConnect(userId)) {
                sendAuthFailureMessage(ctx, "用户账号异常，无法连接", "USER_BLOCKED");
                return;
            }

            // 检查并发连接限制
            if (!checkConcurrentConnectionLimit(userId)) {
                sendAuthFailureMessage(ctx, "连接数超过限制", "CONNECTION_LIMIT");
                return;
            }

            // 认证成功后，移除握手超时检测器，添加正常的心跳超时检测
            if (ctx.pipeline().get("handshake-timeout") != null) {
                ctx.pipeline().remove("handshake-timeout");
                // 添加正常的心跳超时检测：读超时60秒，写超时30秒
                ctx.pipeline().addLast("heartbeat-timeout", new IdleStateHandler(60, 30, 0, TimeUnit.SECONDS));
                log.info("已切换到心跳超时检测: userId={}, channelId={}", userId, ctx.channel().id().asShortText());
            }

            // 注册连接
            log.info("开始注册用户连接: userId={}, channelId={}", userId, ctx.channel().id().asShortText());
            connectionManager.addConnection(ctx.channel(), userId);
            log.info("用户连接注册完成: userId={}, channelId={}", userId, ctx.channel().id().asShortText());

            // 发送离线消息
            log.info("开始发送离线消息: userId={}", userId);
            offlineMessageService.sendOfflineMessagesToUser(userId);
            log.info("离线消息发送完成: userId={}", userId);

            // 发送认证成功消息
            WebSocketMessage response = WebSocketMessage.builder()
                    .type("auth_success")
                    .data(java.util.Map.of(
                        "userId", userId,
                        "username", authResult.getUsername(),
                        "nickname", authResult.getNickname(),
//                        "avatar", authResult.getAvatar(),
                        "serverTime", System.currentTimeMillis(),
                        "sessionId", ctx.channel().id().asShortText()
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();

            sendMessage(ctx, response);

            // 记录认证成功
            recordAuthSuccess(ctx, userId);

            log.info("用户认证成功: userId={}, username={}, channelId={}",
                userId, authResult.getUsername(), ctx.channel().id().asShortText());

            // 自动加入默认房间
            try {
                String defaultRoomId = "default";
                // 直接加入默认房间（连接管理器层面，默认房间是虚拟房间）
                connectionManager.joinRoom(ctx.channel(), defaultRoomId);
                log.info("用户自动加入默认房间: userId={}, roomId={}", userId, defaultRoomId);

            } catch (Exception e) {
                log.warn("用户自动加入默认房间失败: userId={}", userId, e);
                // 不影响认证流程，继续执行
            }

            // 异步广播用户列表更新（用户上线）
            broadcastUserListUpdateAsync();

        } catch (Exception e) {
            log.error("处理认证消息失败: channelId={}", ctx.channel().id().asShortText(), e);
            sendAuthFailureMessage(ctx, "认证处理异常", "INTERNAL_ERROR");
        }
    }

    /**
     * 发送认证失败消息
     */
    private void sendAuthFailureMessage(ChannelHandlerContext ctx, String message, String errorCode) {
        WebSocketMessage errorMessage = WebSocketMessage.builder()
                .type("auth_failure")
                .data(java.util.Map.of(
                    "message", message,
                    "errorCode", errorCode,
                    "timestamp", System.currentTimeMillis(),
                    "retryAfter", getRetryAfterSeconds(errorCode)
                ))
                .timestamp(System.currentTimeMillis())
                .build();
        sendMessage(ctx, errorMessage);
    }

    /**
     * 检查认证频率限制
     */
    private boolean checkAuthRateLimit(ChannelHandlerContext ctx) {
        try {
            String clientIp = getClientIp(ctx);
            String rateLimitKey = "chat:auth:rate_limit:" + clientIp;

            String countStr = (String) redisTemplate.opsForValue().get(rateLimitKey);
            int count = countStr != null ? Integer.parseInt(countStr) : 0;

            if (count >= 30) { // 每分钟最多10次认证请求
                return false;
            }

            redisTemplate.opsForValue().set(rateLimitKey, String.valueOf(count + 1), 60, java.util.concurrent.TimeUnit.SECONDS);
            return true;

        } catch (Exception e) {
            log.error("检查认证频率限制失败", e);
            return true; // 出错时允许认证
        }
    }

    /**
     * 检查用户是否允许连接
     */
    private boolean isUserAllowedToConnect(Long userId) {
        try {
            String blockKey = "chat:user:blocked:" + userId;
            return !Boolean.TRUE.equals(redisTemplate.hasKey(blockKey));
        } catch (Exception e) {
            log.error("检查用户连接权限失败: userId={}", userId, e);
            return true; // 出错时允许连接
        }
    }

    /**
     * 检查并发连接限制
     */
    private boolean checkConcurrentConnectionLimit(Long userId) {
        try {
            int currentConnections = connectionManager.getUserConnectionCount(userId);
            int maxConnections = 5; // 每个用户最多5个连接

            return currentConnections < maxConnections;
        } catch (Exception e) {
            log.error("检查并发连接限制失败: userId={}", userId, e);
            return true; // 出错时允许连接
        }
    }

    /**
     * 记录认证成功
     */
    private void recordAuthSuccess(ChannelHandlerContext ctx, Long userId) {
        try {
            String clientIp = getClientIp(ctx);
            String logKey = "chat:auth:success:" + userId;

            java.util.Map<String, Object> authLog = java.util.Map.of(
                "userId", userId,
                "clientIp", clientIp,
                "userAgent", getUserAgent(ctx),
                "timestamp", System.currentTimeMillis(),
                "channelId", ctx.channel().id().asShortText()
            );

            redisTemplate.opsForValue().set(logKey, JSON.toJSONString(authLog), 24, java.util.concurrent.TimeUnit.HOURS);

        } catch (Exception e) {
            log.error("记录认证成功日志失败: userId={}", userId, e);
        }
    }

    /**
     * 记录认证失败
     */
    private void recordAuthFailure(ChannelHandlerContext ctx, String errorCode) {
        try {
            String clientIp = getClientIp(ctx);
            String failureKey = "chat:auth:failure:" + clientIp;

            String countStr = (String) redisTemplate.opsForValue().get(failureKey);
            int count = countStr != null ? Integer.parseInt(countStr) : 0;

            redisTemplate.opsForValue().set(failureKey, String.valueOf(count + 1), 3600, java.util.concurrent.TimeUnit.SECONDS);

            // 如果失败次数过多，临时封禁IP
            if (count >= 20) {
                String blockKey = "chat:ip:blocked:" + clientIp;
                redisTemplate.opsForValue().set(blockKey, "true", 1800, java.util.concurrent.TimeUnit.SECONDS); // 封禁30分钟
                log.warn("IP认证失败次数过多，临时封禁: ip={}, count={}", clientIp, count + 1);
            }

        } catch (Exception e) {
            log.error("记录认证失败日志失败", e);
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ChannelHandlerContext ctx) {
        try {
            java.net.InetSocketAddress remoteAddress = (java.net.InetSocketAddress) ctx.channel().remoteAddress();
            return remoteAddress.getAddress().getHostAddress();
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取用户代理
     */
    private String getUserAgent(ChannelHandlerContext ctx) {
        // 这里需要从WebSocket握手时的HTTP头中获取User-Agent
        // 简化实现，实际应该在握手时保存
        return "WebSocket-Client";
    }

    /**
     * 获取重试等待时间
     */
    private int getRetryAfterSeconds(String errorCode) {
        switch (errorCode) {
            case "RATE_LIMIT":
                return 60;
            case "CONNECTION_LIMIT":
                return 30;
            case "USER_BLOCKED":
                return 3600;
            default:
                return 5;
        }
    }

    /**
     * 处理聊天消息
     */
    private void handleChatMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            Long userId = connectionManager.getUserId(ctx.channel());
            if (userId == null) {
                sendErrorMessage(ctx, "请先进行认证");
                return;
            }

            // 解析聊天消息
            ChatMessage chatMessage = JSON.parseObject(
                JSON.toJSONString(message.getData()), ChatMessage.class);

            if (chatMessage == null) {
                sendErrorMessage(ctx, "无效的聊天消息格式");
                return;
            }

            // 验证聊天消息
            if (!validateChatMessage(chatMessage)) {
                sendErrorMessage(ctx, "聊天消息验证失败");
                return;
            }

            // 设置消息基本信息
            chatMessage.setSenderId(userId);
            chatMessage.setSendTime(System.currentTimeMillis());

            // 生成消息ID
            if (chatMessage.getMessageId() == null || chatMessage.getMessageId().trim().isEmpty()) {
                chatMessage.setMessageId("msg_" + System.currentTimeMillis() + "_" + userId);
            }

            // 处理聊天消息
            chatMessageService.handleChatMessage(chatMessage);

            // 发送确认消息
            WebSocketMessage ackMessage = WebSocketMessage.builder()
                    .type("message_ack")
                    .data(java.util.Map.of(
                        "messageId", chatMessage.getMessageId(),
                        "status", "sent",
                        "timestamp", chatMessage.getSendTime()
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();
            sendMessage(ctx, ackMessage);

            log.info("处理聊天消息: senderId={}, messageId={}, roomId={}, content={}",
                userId, chatMessage.getMessageId(), chatMessage.getRoomId(),
                chatMessage.getContent().length() > 50 ?
                    chatMessage.getContent().substring(0, 50) + "..." :
                    chatMessage.getContent());

        } catch (Exception e) {
            log.error("处理聊天消息失败", e);
            sendErrorMessage(ctx, "发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 验证聊天消息
     */
    private boolean validateChatMessage(ChatMessage chatMessage) {
        // 验证消息内容（回复消息可以没有内容）
        if (!chatMessage.isReplyMessage() && 
            (chatMessage.getContent() == null || chatMessage.getContent().trim().isEmpty())) {
            return false;
        }

        // 验证消息长度
        if (chatMessage.getContent() != null && chatMessage.getContent().length() > 1000) {
            return false;
        }

        // 验证消息类型
        if (chatMessage.getMessageType() == null || chatMessage.getMessageType().trim().isEmpty()) {
            chatMessage.setMessageType("text"); // 默认为文本消息
        }

        // 验证房间ID或接收者ID
        if (chatMessage.isPrivateMessage()) {
            if (chatMessage.getReceiverId() == null || chatMessage.getReceiverId() <= 0) {
                return false;
            }
        } else if (chatMessage.isGroupMessage()) {
            if (chatMessage.getRoomId() == null || chatMessage.getRoomId().trim().isEmpty()) {
                return false;
            }
        } else {
            return false; // 必须是私聊或群聊
        }

        // 验证引用消息（如果存在）
        if (chatMessage.getReferenceMessageId() != null && !chatMessage.getReferenceMessageId().trim().isEmpty()) {
            // 引用消息需要有发送者ID
            if (chatMessage.getReferenceSenderId() == null || chatMessage.getReferenceSenderId() <= 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 处理加入房间消息
     */
    private void handleJoinRoomMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            Long userId = connectionManager.getUserId(ctx.channel());
            if (userId == null) {
                sendErrorMessage(ctx, "请先进行认证");
                return;
            }

            String roomId = (String) message.getData().get("roomId");
            if (roomId == null || roomId.trim().isEmpty()) {
                sendErrorMessage(ctx, "房间ID不能为空");
                return;
            }

            // 检查用户是否可以加入房间
            if (!chatRoomService.canJoinRoom(roomId, userId)) {
                sendErrorMessage(ctx, "无权限加入该房间");
                return;
            }

            try {
                // 加入房间（业务层处理）
                chatRoomService.joinRoom(roomId, userId);

                // 连接管理器中记录
                connectionManager.joinRoom(ctx.channel(), roomId);
            } catch (Exception e) {
                log.error("用户加入房间失败: userId={}, roomId={}", userId, roomId, e);
                sendErrorMessage(ctx, "加入房间失败: " + e.getMessage());
                return;
            }

            // 发送加入成功消息
            WebSocketMessage response = WebSocketMessage.builder()
                    .type("join_room_success")
                    .data(java.util.Map.of("roomId", roomId))
                    .timestamp(System.currentTimeMillis())
                    .build();

            sendMessage(ctx, response);
            log.info("用户加入房间: userId={}, roomId={}", userId, roomId);

        } catch (Exception e) {
            log.error("处理加入房间消息失败", e);
            sendErrorMessage(ctx, "加入房间失败: " + e.getMessage());
        }
    }

    /**
     * 处理离开房间消息
     */
    private void handleLeaveRoomMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            Long userId = connectionManager.getUserId(ctx.channel());
            if (userId == null) {
                sendErrorMessage(ctx, "请先进行认证");
                return;
            }

            String roomId = (String) message.getData().get("roomId");
            if (roomId == null || roomId.trim().isEmpty()) {
                sendErrorMessage(ctx, "房间ID不能为空");
                return;
            }

            try {
                // 离开房间（业务层处理）
                chatRoomService.leaveRoom(roomId, userId);

                // 连接管理器中移除
                connectionManager.leaveRoom(ctx.channel(), roomId);
            } catch (Exception e) {
                log.error("用户离开房间失败: userId={}, roomId={}", userId, roomId, e);
                sendErrorMessage(ctx, "离开房间失败: " + e.getMessage());
                return;
            }

            // 发送离开成功消息
            WebSocketMessage response = WebSocketMessage.builder()
                    .type("leave_room_success")
                    .data(java.util.Map.of("roomId", roomId))
                    .timestamp(System.currentTimeMillis())
                    .build();

            sendMessage(ctx, response);
            log.info("用户离开房间: userId={}, roomId={}", userId, roomId);

        } catch (Exception e) {
            log.error("处理离开房间消息失败", e);
            sendErrorMessage(ctx, "离开房间失败: " + e.getMessage());
        }
    }

    /**
     * 处理心跳消息
     */
    private void handlePingMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        WebSocketMessage pong = WebSocketMessage.builder()
                .type("pong")
                .data(java.util.Map.of("timestamp", System.currentTimeMillis()))
                .timestamp(System.currentTimeMillis())
                .build();
        sendMessage(ctx, pong);
    }

    /**
     * 处理心跳消息
     */
    private void handleHeartbeatMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        // 心跳消息处理，更新最后活跃时间
        Long userId = connectionManager.getUserId(ctx.channel());
        if (userId != null) {
            log.debug("收到心跳消息: userId={}, channelId={}", userId, ctx.channel().id().asShortText());

            // 发送心跳响应
            WebSocketMessage heartbeatResponse = WebSocketMessage.builder()
                    .type("heartbeat_ack")
                    .data(java.util.Map.of(
                        "timestamp", System.currentTimeMillis(),
                        "status", "ok"
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();
            sendMessage(ctx, heartbeatResponse);
        } else {
            // 未认证用户发送心跳，要求先认证
            sendErrorMessage(ctx, "请先进行认证");
        }
    }

    /**
     * 用户事件处理（包括超时事件）
     */
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            Long userId = connectionManager.getUserId(ctx.channel());

            log.info("收到空闲状态事件: channelId={}, userId={}, state={}, remoteAddress={}",
                ctx.channel().id().asShortText(), userId, event.state(), ctx.channel().remoteAddress());

            if (event.state() == IdleState.READER_IDLE) {
                // 检查握手是否完成
                Object handshakeCompletedObj = ctx.channel().attr(io.netty.util.AttributeKey.valueOf("handshake_completed")).get();
                Boolean handshakeCompleted = (handshakeCompletedObj instanceof Boolean) ? (Boolean) handshakeCompletedObj : false;

                if (handshakeCompleted == null || !handshakeCompleted) {
                    // 握手超时 - 连接建立后没有收到 WebSocket 消息
                    log.warn("WebSocket连接握手超时: channelId={}, userId={}, remoteAddress={}, 连接建立后未收到WebSocket消息",
                        ctx.channel().id().asShortText(), userId, ctx.channel().remoteAddress());

                    // 直接关闭连接，不发送消息（因为握手都没完成）
                    ctx.close();
                } else {
                    // 心跳读超时 - WebSocket 连接已建立但长时间没有消息
                    log.warn("WebSocket连接心跳超时: channelId={}, userId={}, remoteAddress={}",
                        ctx.channel().id().asShortText(), userId, ctx.channel().remoteAddress());
                    ctx.close();
                }
            } else if (event.state() == IdleState.WRITER_IDLE) {
                // 写超时，发送心跳
                WebSocketMessage ping = WebSocketMessage.builder()
                        .type("ping")
                        .data(java.util.Map.of("timestamp", System.currentTimeMillis()))
                        .timestamp(System.currentTimeMillis())
                        .build();
                sendMessage(ctx, ping);
                log.debug("发送心跳消息: channelId={}, userId={}", ctx.channel().id().asShortText(), userId);
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    /**
     * 异常处理
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        Long userId = connectionManager.getUserId(ctx.channel());
        log.error("WebSocket连接异常: channelId={}, userId={}, remoteAddress={}, cause={}",
            ctx.channel().id().asShortText(), userId, ctx.channel().remoteAddress(), cause.getMessage(), cause);

        // 发送错误消息给客户端（如果连接还活着）
        if (ctx.channel().isActive()) {
            try {
                sendErrorMessage(ctx, "连接异常: " + cause.getMessage());
            } catch (Exception e) {
                log.warn("发送错误消息失败", e);
            }
        }

        ctx.close();
    }

    /**
     * 发送消息
     */
    private void sendMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        String json = JSON.toJSONString(message);
        ctx.writeAndFlush(new TextWebSocketFrame(json));
    }

    /**
     * 处理获取在线用户消息
     */
    private void handleGetOnlineUsersMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            Long userId = connectionManager.getUserId(ctx.channel());
            if (userId == null) {
                sendErrorMessage(ctx, "请先进行认证");
                return;
            }

            String roomId = (String) message.getData().get("roomId");
            if (roomId == null || roomId.trim().isEmpty()) {
                sendErrorMessage(ctx, "房间ID不能为空");
                return;
            }

            // 获取房间在线用户列表
            java.util.Set<Long> onlineUserIds = connectionManager.getRoomUsers(roomId);

            // 获取用户详细信息
            java.util.List<UserInfo> userInfoList = userInfoService.getUserInfoList(onlineUserIds);

            WebSocketMessage response = WebSocketMessage.builder()
                    .type("user_list")
                    .data(java.util.Map.of(
                        "roomId", roomId,
                        "users", userInfoList,
                        "count", userInfoList.size()
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();

            sendMessage(ctx, response);
            log.debug("获取在线用户列表: roomId={}, count={}", roomId, userInfoList.size());

        } catch (Exception e) {
            log.error("获取在线用户失败", e);
            sendErrorMessage(ctx, "获取在线用户失败: " + e.getMessage());
        }
    }

    /**
     * 处理获取房间信息消息
     */
    private void handleGetRoomInfoMessage(ChannelHandlerContext ctx, WebSocketMessage message) {
        try {
            Long userId = connectionManager.getUserId(ctx.channel());
            if (userId == null) {
                sendErrorMessage(ctx, "请先进行认证");
                return;
            }

            String roomId = (String) message.getData().get("roomId");
            if (roomId == null || roomId.trim().isEmpty()) {
                sendErrorMessage(ctx, "房间ID不能为空");
                return;
            }

            // 获取房间信息（这里需要调用ChatRoomService）
            // 简化实现，实际应该注入ChatRoomService
            java.util.Set<Long> roomUsers = connectionManager.getRoomUsers(roomId);

            WebSocketMessage response = WebSocketMessage.builder()
                    .type("room_info")
                    .data(java.util.Map.of(
                        "roomId", roomId,
                        "userCount", roomUsers.size(),
                        "users", roomUsers
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();

            sendMessage(ctx, response);
            log.debug("获取房间信息: roomId={}, userCount={}", roomId, roomUsers.size());

        } catch (Exception e) {
            log.error("获取房间信息失败", e);
            sendErrorMessage(ctx, "获取房间信息失败: " + e.getMessage());
        }
    }

    /**
     * 解析WebSocket消息
     */
    private WebSocketMessage parseWebSocketMessage(String text) {
        try {
            return JSON.parseObject(text, WebSocketMessage.class);
        } catch (Exception e) {
            log.warn("解析WebSocket消息失败: {}", text, e);
            return null;
        }
    }

    /**
     * 验证消息格式
     */
    private boolean validateMessage(WebSocketMessage message) {
        if (message.getType() == null || message.getType().trim().isEmpty()) {
            return false;
        }

        if (message.getData() == null) {
            message.setData(new java.util.HashMap<>());
        }

        // 验证消息类型是否在允许的范围内
        java.util.Set<String> allowedTypes = java.util.Set.of(
            "auth", "chat", "join_room", "leave_room", "ping", "heartbeat",
            "get_online_users", "get_room_info"
        );

        return allowedTypes.contains(message.getType());
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(ChannelHandlerContext ctx, String error) {
        WebSocketMessage errorMessage = WebSocketMessage.builder()
                .type("error")
                .data(java.util.Map.of(
                    "message", error,
                    "timestamp", System.currentTimeMillis()
                ))
                .timestamp(System.currentTimeMillis())
                .build();
        sendMessage(ctx, errorMessage);
    }

    /**
     * 异步广播用户列表更新给所有在线用户
     */
    private void broadcastUserListUpdateAsync() {
        CompletableFuture.runAsync(this::broadcastUserListUpdate, broadcastExecutor)
            .exceptionally(throwable -> {
                log.error("异步广播用户列表更新失败", throwable);
                return null;
            });
    }

    /**
     * 广播用户列表更新给所有在线用户
     */
    private void broadcastUserListUpdate() {
        try {
            // 获取所有在线用户ID
            java.util.Set<Long> onlineUserIds = connectionManager.getOnlineUserIds();

            if (onlineUserIds.isEmpty()) {
                log.debug("没有在线用户，跳过用户列表广播");
                return;
            }

            // 获取用户详细信息
            java.util.List<UserInfo> userInfoList = userInfoService.getUserInfoList(onlineUserIds);

            // 构建用户列表消息
            WebSocketMessage userListMessage = WebSocketMessage.builder()
                    .type("user_list")
                    .data(java.util.Map.of(
                        "users", userInfoList,
                        "count", userInfoList.size(),
                        "timestamp", System.currentTimeMillis()
                    ))
                    .timestamp(System.currentTimeMillis())
                    .build();

            // 广播给所有在线用户
            int sentCount = connectionManager.broadcastToAll(userListMessage, null);
            log.info("广播用户列表更新: 在线用户数={}, 发送成功数={}", userInfoList.size(), sentCount);

            // 如果发送成功数与在线用户数不匹配，记录警告
            if (sentCount != userInfoList.size()) {
                log.warn("用户列表广播不完整: 期望发送={}, 实际发送={}", userInfoList.size(), sentCount);
            }

        } catch (Exception e) {
            log.error("广播用户列表更新失败", e);
        }
    }
}
