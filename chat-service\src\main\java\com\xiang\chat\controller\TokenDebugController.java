package com.xiang.chat.controller;

import com.xiang.chat.code.R;
import com.xiang.chat.config.JwtTokenProvider;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.service.JwtValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TokenDebugController {

    private final JwtTokenProvider jwtTokenProvider;
    private final JwtValidationService jwtValidationService;

    @GetMapping("/token-debug")
    public Map<String, Object> debugToken() {
        Map<String, Object> result = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        result.put("authenticated", authentication != null && authentication.isAuthenticated());
        result.put("username", authentication != null ? authentication.getName() : "null");
        result.put("authType", authentication != null ? authentication.getClass().getSimpleName() : "null");
        result.put("principal",
                authentication != null ? authentication.getPrincipal().getClass().getSimpleName() : "null");
        result.put("detailsType",
                authentication != null && authentication.getDetails() != null
                        ? authentication.getDetails().getClass().getSimpleName()
                        : "null");

        // 检查是否是JwtAuthenticationToken
        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            Jwt jwt = jwtAuth.getToken();
            result.put("isJwtAuthenticationToken", true);
            result.put("jwtSubject", jwt.getSubject());
            result.put("jwtIssuer", jwt.getIssuer() != null ? jwt.getIssuer().toString() : "null");
            result.put("jwtTokenLength", jwt.getTokenValue().length());
            result.put("jwtTokenPreview",
                    jwt.getTokenValue().substring(0, Math.min(20, jwt.getTokenValue().length())) + "...");
        } else {
            result.put("isJwtAuthenticationToken", false);
        }

        // 测试JwtTokenProvider
        String token = jwtTokenProvider.getCurrentToken();
        result.put("providerHasToken", token != null);
        result.put("providerTokenLength", token != null ? token.length() : 0);
        result.put("providerTokenPreview",
                token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null");

        log.info("Token debug result: {}", result);

        return result;
    }

    @GetMapping("/feign-test")
    public Map<String, Object> testFeignCall() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 详细检查认证状态
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            result.put("authenticationPresent", authentication != null);
            result.put("authenticationName", authentication != null ? authentication.getName() : "null");
            result.put("authenticationClass", authentication != null ? authentication.getClass().getSimpleName() : "null");
            result.put("isAuthenticated", authentication != null ? authentication.isAuthenticated() : false);

            // 检查details中的token
            if (authentication != null && authentication.getDetails() != null) {
                Object details = authentication.getDetails();
                result.put("detailsClass", details.getClass().getSimpleName());

                if (details instanceof com.xiang.chat.service.OAuth2TokenResponse tokenResponse) {
                    result.put("tokenInDetails", tokenResponse.getAccessToken() != null);
                    result.put("tokenTypeInDetails", tokenResponse.getTokenType());
                    if (tokenResponse.getAccessToken() != null) {
                        result.put("tokenLengthInDetails", tokenResponse.getAccessToken().length());
                        result.put("tokenPreviewInDetails", tokenResponse.getAccessToken().substring(0, Math.min(20, tokenResponse.getAccessToken().length())) + "...");
                    }
                }
            }

            // 测试JwtTokenProvider
            String token = jwtTokenProvider.getCurrentToken();
            result.put("tokenAvailable", token != null);
            result.put("tokenLength", token != null ? token.length() : 0);

            if (token != null) {
                result.put("tokenPreview", token.substring(0, Math.min(20, token.length())) + "...");
                log.info("Feign调用将使用token: {}", token.substring(0, Math.min(20, token.length())) + "...");
                result.put("feignCallReady", true);

                // 测试实际的Feign调用
                try {
                    // 这里可以调用一个实际的auth-service接口来验证token传递
                    result.put("feignTestResult", "Token传递测试准备就绪");
                } catch (Exception feignEx) {
                    result.put("feignTestError", feignEx.getMessage());
                    log.error("Feign调用测试失败", feignEx);
                }
            } else {
                result.put("feignCallReady", false);
                result.put("error", "No JWT token available for Feign call");
            }

        } catch (Exception e) {
            result.put("error", e.getMessage());
            log.error("Feign test failed", e);
        }

        return result;
    }

    @GetMapping("/test-feign-call")
    public Map<String, Object> testActualFeignCall() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 首先检查token状态
            String token = jwtTokenProvider.getCurrentToken();
            result.put("tokenAvailable", token != null);

            if (token != null) {
                result.put("tokenLength", token.length());
                result.put("tokenPreview", token.substring(0, Math.min(20, token.length())) + "...");

                // 尝试调用auth-service的JWT验证接口
                try {
                    R<UserInfo> validationResult = jwtValidationService.validateToken(token);
                    result.put("feignCallSuccess", true);
                    result.put("validationSuccess", validationResult.isSuccess());
                    result.put("validationMessage", validationResult.getMessage());

                    if (validationResult.isSuccess() && validationResult.getData() != null) {
                        UserInfo userInfo = validationResult.getData();
                        result.put("userInfo", Map.of(
                            "id", userInfo.getId(),
                            "username", userInfo.getUsername(),
                            "nickname", userInfo.getNickname()
                        ));
                    }

                } catch (Exception feignEx) {
                    result.put("feignCallSuccess", false);
                    result.put("feignError", feignEx.getMessage());
                    log.error("Feign调用失败", feignEx);
                }
            } else {
                result.put("error", "No JWT token available for Feign call");
            }

        } catch (Exception e) {
            result.put("error", e.getMessage());
            log.error("测试Feign调用失败", e);
        }

        return result;
    }

    @GetMapping("/test-user-api")
    public Map<String, Object> testUserApi() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("=== 开始测试用户API调用 ===");

            // 这里需要注入AuthFeignService来测试实际调用
            // 由于这是测试控制器，我们先返回token状态
            String token = jwtTokenProvider.getCurrentToken();
            result.put("tokenReady", token != null);

            if (token != null) {
                result.put("message", "Token准备就绪，可以进行Feign调用");
                result.put("tokenLength", token.length());
                log.info("Token准备就绪，长度: {}", token.length());
            } else {
                result.put("message", "Token不可用，Feign调用将失败");
                log.warn("Token不可用");
            }

            log.info("=== 用户API调用测试完成 ===");

        } catch (Exception e) {
            result.put("error", e.getMessage());
            log.error("用户API测试失败", e);
        }

        return result;
    }
}