package com.xiang.chat.controller;

import com.xiang.chat.config.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TokenDebugController {

    private final JwtTokenProvider jwtTokenProvider;

    @GetMapping("/token-debug")
    public Map<String, Object> debugToken() {
        Map<String, Object> result = new HashMap<>();

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        result.put("authenticated", authentication != null && authentication.isAuthenticated());
        result.put("username", authentication != null ? authentication.getName() : "null");
        result.put("authType", authentication != null ? authentication.getClass().getSimpleName() : "null");
        result.put("principal",
                authentication != null ? authentication.getPrincipal().getClass().getSimpleName() : "null");
        result.put("detailsType",
                authentication != null && authentication.getDetails() != null
                        ? authentication.getDetails().getClass().getSimpleName()
                        : "null");

        // 检查是否是JwtAuthenticationToken
        if (authentication instanceof JwtAuthenticationToken jwtAuth) {
            Jwt jwt = jwtAuth.getToken();
            result.put("isJwtAuthenticationToken", true);
            result.put("jwtSubject", jwt.getSubject());
            result.put("jwtIssuer", jwt.getIssuer() != null ? jwt.getIssuer().toString() : "null");
            result.put("jwtTokenLength", jwt.getTokenValue().length());
            result.put("jwtTokenPreview",
                    jwt.getTokenValue().substring(0, Math.min(20, jwt.getTokenValue().length())) + "...");
        } else {
            result.put("isJwtAuthenticationToken", false);
        }

        // 测试JwtTokenProvider
        String token = jwtTokenProvider.getCurrentToken();
        result.put("providerHasToken", token != null);
        result.put("providerTokenLength", token != null ? token.length() : 0);
        result.put("providerTokenPreview",
                token != null ? token.substring(0, Math.min(20, token.length())) + "..." : "null");

        log.info("Token debug result: {}", result);

        return result;
    }

    @GetMapping("/feign-test")
    public Map<String, Object> testFeignCall() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 测试JwtTokenProvider
            String token = jwtTokenProvider.getCurrentToken();
            result.put("tokenAvailable", token != null);
            result.put("tokenLength", token != null ? token.length() : 0);

            if (token != null) {
                result.put("tokenPreview", token.substring(0, Math.min(20, token.length())) + "...");
                log.info("Feign调用将使用token: {}", token.substring(0, Math.min(20, token.length())) + "...");
                result.put("feignCallReady", true);

                // 测试实际的Feign调用
                try {
                    // 这里可以调用一个实际的auth-service接口来验证token传递
                    result.put("feignTestResult", "Token传递测试准备就绪");
                } catch (Exception feignEx) {
                    result.put("feignTestError", feignEx.getMessage());
                    log.error("Feign调用测试失败", feignEx);
                }
            } else {
                result.put("feignCallReady", false);
                result.put("error", "No JWT token available for Feign call");
            }

        } catch (Exception e) {
            result.put("error", e.getMessage());
            log.error("Feign test failed", e);
        }

        return result;
    }

    @GetMapping("/test-user-api")
    public Map<String, Object> testUserApi() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("=== 开始测试用户API调用 ===");

            // 这里需要注入AuthFeignService来测试实际调用
            // 由于这是测试控制器，我们先返回token状态
            String token = jwtTokenProvider.getCurrentToken();
            result.put("tokenReady", token != null);

            if (token != null) {
                result.put("message", "Token准备就绪，可以进行Feign调用");
                result.put("tokenLength", token.length());
                log.info("Token准备就绪，长度: {}", token.length());
            } else {
                result.put("message", "Token不可用，Feign调用将失败");
                log.warn("Token不可用");
            }

            log.info("=== 用户API调用测试完成 ===");

        } catch (Exception e) {
            result.put("error", e.getMessage());
            log.error("用户API测试失败", e);
        }

        return result;
    }
}