package com.xiang.chat.config;

import feign.RequestInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Auth服务Feign配置
 * 为AuthFeignService配置JWT token拦截器
 */
@Configuration
@RequiredArgsConstructor
public class AuthFeignConfig {

    private final AuthFeignRequestInterceptor authFeignRequestInterceptor;

    /**
     * 为auth-service配置请求拦截器
     * @return RequestInterceptor
     */
    @Bean
    public RequestInterceptor authServiceRequestInterceptor() {
        return authFeignRequestInterceptor;
    }
}