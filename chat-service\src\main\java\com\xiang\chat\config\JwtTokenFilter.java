package com.xiang.chat.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT Token过滤器
 * 确保每个请求的JWT token都能被正确传递到子线程中
 */
@Slf4j
@Component
@Order(1)
@RequiredArgsConstructor
public class JwtTokenFilter extends OncePerRequestFilter {

    private final JwtTokenProvider jwtTokenProvider;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        try {
            String token = null;

            // 从请求头中提取JWT token
            String authHeader = request.getHeader("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
                log.debug("Found JWT token in Authorization header, length: {}", token.length());
            }

            // 如果请求头中没有token，尝试从SecurityContext中获取
            if (token == null) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication != null && authentication.getDetails() instanceof com.xiang.chat.service.OAuth2TokenResponse tokenResponse) {
                    token = tokenResponse.getAccessToken();
                    if (token != null) {
                        log.debug("Found JWT token in SecurityContext details, length: {}", token.length());
                    }
                }
            }

            // 将token设置到ThreadLocal中，确保子线程可以访问
            if (token != null) {
                jwtTokenProvider.setCurrentToken(token);
                log.debug("Set JWT token to ThreadLocal, length: {}", token.length());
            }

            // 继续过滤器链
            filterChain.doFilter(request, response);

        } finally {
            // 请求结束后清理ThreadLocal，避免内存泄漏
            jwtTokenProvider.clearCurrentToken();
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        // 对于静态资源和公开接口，不需要处理token
        String path = request.getRequestURI();
        return path.startsWith("/static/") || 
               path.startsWith("/css/") || 
               path.startsWith("/js/") || 
               path.startsWith("/images/") ||
               path.equals("/login") ||
               path.equals("/register") ||
               path.startsWith("/test/");
    }
}
