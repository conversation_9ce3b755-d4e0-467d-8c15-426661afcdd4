package com.xiang.chat.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * OAuth2专用Feign客户端
 * 不使用AuthFeignRequestInterceptor，避免JWT token冲突
 */
@FeignClient(name = "auth-service", 
             contextId = "oauth2-client",
             fallback = OAuth2FeignService.OAuth2FeignServiceFallback.class)
public interface OAuth2FeignService {

    /**
     * OAuth2 密码模式获取token
     */
    @PostMapping(value = "/oauth2/token", consumes = "application/x-www-form-urlencoded")
    Map<String, Object> getOAuth2Token(
            @RequestHeader("Authorization") String authorization,
            @RequestParam("grant_type") String grantType,
            @RequestParam("username") String username,
            @RequestParam("password") String password,
            @RequestParam("scope") String scope);

    /**
     * Fallback实现
     */
    class OAuth2FeignServiceFallback implements OAuth2FeignService {

        @Override
        public Map<String, Object> getOAuth2Token(String authorization, String grantType, String username,
                String password, String scope) {
            return Map.of(
                    "error", "service_unavailable",
                    "error_description", "认证服务不可用，请稍后重试");
        }
    }
}