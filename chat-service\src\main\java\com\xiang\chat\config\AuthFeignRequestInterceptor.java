package com.xiang.chat.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Auth服务Feign请求拦截器
 * 自动为调用auth-service的请求添加JWT token
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthFeignRequestInterceptor implements RequestInterceptor {

    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public void apply(RequestTemplate template) {
        // 只对auth-service的用户相关接口添加token
        String url = template.url();
        log.debug("=== Feign Request Interceptor ===");
        log.debug("Request URL: {}", url);
        log.debug("Needs authentication: {}", needsAuthentication(url));
        
        if (needsAuthentication(url)) {
            String token = jwtTokenProvider.getCurrentToken();
            log.debug("Token from provider: {}", token != null ? "present (length: " + token.length() + ")" : "null");
            
            if (token != null && !token.trim().isEmpty()) {
                template.header("Authorization", "Bearer " + token);
                log.info("✅ Added JWT token to Feign request: {} (token length: {})", url, token.length());
            } else {
                log.warn("❌ No JWT token available for authenticated request: {}", url);
            }
        } else {
            log.debug("⏭️ Skipping token addition for URL: {}", url);
        }
        log.debug("=== End Feign Request Interceptor ===");
    }

    /**
     * 判断请求是否需要认证
     * 
     * @param url 请求URL
     * @return true如果需要认证，否则false
     */
    private boolean needsAuthentication(String url) {
        // OAuth2 token端点使用Basic认证，不需要JWT token
        if (url.startsWith("/oauth2/token") || url.contains("/oauth2/token")) {
            return false;
        }

        // JWT验证接口不需要认证（它本身就是用来验证token的）
        if (url.startsWith("/api/jwt/validate") || url.contains("/api/jwt/validate")) {
            return false;
        }

        // 需要JWT认证的接口
        return url.startsWith("/api/users/") ||
                url.contains("/api/users/") ||
                url.startsWith("/api/jwt/userinfo") ||
                url.contains("/api/jwt/userinfo");
    }
}